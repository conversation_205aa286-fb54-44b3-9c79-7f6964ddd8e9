# DynamoDB Models Package

# Import existing models
from .fund import (
    Fund,
    FundCreate,
    FundUpdate,
    FundResponse,
    FundType,
    FundStatus,
    RiskLevel,
    Currency,
    PerformanceMetrics,
    Holdings,
    FundDynamoDBItem,
)

from .user import (
    User,
    UserCreate,
    UserUpdate,
    UserResponse,
    UserRole,
    UserStatus,
    Department,
    UserPreferences,
    PermissionSet,
    SessionInfo,
    UserLogin,
    UserPasswordReset,
    UserPasswordChange,
    UserDynamoDBItem,
)

from .report import (
    Report,
    ReportCreate,
    ReportUpdate,
    ReportType,
    ReportStatus,
    ReportFormat,
)

# Import new fund data models
from .fund_data import (
    BaseItem,
    FundInfo,
    FundStats,
    MonthlyReturn,
    CalendarYearReturn,
    ExposureSnapshot,
    MarketCapExposure,
    FundDataDynamoDBItem,
    generate_fund_info_pk,
    generate_fund_info_sk,
    generate_fund_stats_sk,
    generate_monthly_return_sk,
    generate_calendar_year_return_sk,
    generate_exposure_sk,
    generate_market_cap_exposure_sk,
)

# Import market data models
from .market_data import (
    MarketDataSource,
    DataQuality,
    PriceType,
    MarketDataPoint,
    PriceData,
    ValuationMetrics,
    TechnicalIndicators,
    RiskAnalytics,
    MarketDataInput,
    BenchmarkData,
    MarketDataSummary,
)

# Import relationship models
from .relationships import (
    BaseEntity,
    FundReference,
    UserReference,
    ReportReference,
    FundSubscription,
    UserPortfolio,
    AuditLog,
    FundHierarchy,
    InvestmentStrategy,
    FundManager,
    ReportSchedule,
    UserFundAccess,
    FundBenchmark,
    FundComparison,
    Notification,
    FundDataSource,
    RelationshipHelpers,
)

# Import request models
from .requests import (
    FundCreateRequest,
    FundUpdateRequest,
    FundQueryRequest,
    UserCreateRequest,
    UserUpdateRequest,
    UserQueryRequest,
    UserPasswordChangeRequest,
    UserPasswordResetRequest,
    UserLoginRequest,
    UserRegisterRequest,
    TokenRefreshRequest,
    ConfirmRegistrationRequest,
    ReportCreateRequest,
    ReportUpdateRequest,
    ReportQueryRequest,
    ReportGenerateRequest,
    BulkOperationRequest,
    PaginationRequest,
    SearchRequest,
)

# Import response models
from .responses import (
    BaseResponse,
    ErrorResponse,
    PaginatedResponse,
    SingleResponse,
    ListResponse,
    BulkOperationResponse,
    FundSummaryResponse,
    FundDetailResponse,
    FundCreateResponse,
    FundUpdateResponse,
    FundListResponse,
    FundSearchResponse,
    UserSummaryResponse,
    UserDetailResponse,
    UserProfileResponse,
    UserCreateResponse,
    UserUpdateResponse,
    UserListResponse,
    UserSearchResponse,
    UserPasswordChangeResponse,
    UserLoginResponse,
    UserRegisterResponse,
    TokenRefreshResponse,
    ConfirmRegistrationResponse,
    ReportSummaryResponse,
    ReportDetailResponse,
    ReportCreateResponse,
    ReportUpdateResponse,
    ReportListResponse,
    ReportGenerateResponse,
    ReportDownloadResponse,
    FundStatisticsResponse,
    UserStatisticsResponse,
    ReportStatisticsResponse,
    HealthCheckResponse,
    SystemInfoResponse,
)

# Import API utilities
from .api_models import (
    ModelConverter,
    PublicFundResponse,
    AdminUserResponse,
    AuditLogEntry,
    ValidationUtils,
    QueryBuilder,
    ResponseFormatter,
)

# Import DynamoDB mapping utilities
from .dynamodb_mapper import (
    DynamoDBMapper,
    DynamoDBTypeConverter,
    FundDynamoDBMapper,
    UserDynamoDBMapper,
    ReportDynamoDBMapper,
    BatchDynamoDBMapper,
)

# Import validation utilities
from .validators import (
    CommonValidators,
    BusinessLogicValidators,
)

# Import portfolio models
from .portfolio import (
    Portfolio,
    PortfolioHolding,
    PortfolioTransaction,
    PortfolioPerformance,
    PortfolioStatus,
    TransactionType,
    PortfolioType,
    PortfolioCreateRequest,
    PortfolioUpdateRequest,
    PortfolioResponse,
    AddHoldingRequest,
    AddTransactionRequest,
    PortfolioDynamoDBItem,
)

# Rebuild core models to resolve forward references
Fund.model_rebuild()
User.model_rebuild()
Report.model_rebuild()
Portfolio.model_rebuild()

__all__ = [
    # Existing Fund models
    "Fund",
    "FundCreate",
    "FundUpdate",
    "FundResponse",
    "FundType",
    "FundStatus",
    "RiskLevel",
    "Currency",
    "PerformanceMetrics",
    "Holdings",
    "FundDynamoDBItem",
    # User models
    "User",
    "UserCreate",
    "UserUpdate",
    "UserResponse",
    "UserRole",
    "UserStatus",
    "Department",
    "UserPreferences",
    "PermissionSet",
    "SessionInfo",
    "UserLogin",
    "UserPasswordReset",
    "UserPasswordChange",
    "UserDynamoDBItem",
    # Report models
    "Report",
    "ReportCreate",
    "ReportUpdate",
    "ReportType",
    "ReportStatus",
    "ReportFormat",
    # New fund data models
    "BaseItem",
    "FundInfo",
    "FundStats",
    "MonthlyReturn",
    "CalendarYearReturn",
    "ExposureSnapshot",
    "MarketCapExposure",
    "FundDataDynamoDBItem",
    "generate_fund_info_pk",
    "generate_fund_info_sk",
    "generate_fund_stats_sk",
    "generate_monthly_return_sk",
    "generate_calendar_year_return_sk",
    "generate_exposure_sk",
    "generate_market_cap_exposure_sk",
    # Market data models
    "MarketDataSource",
    "DataQuality",
    "PriceType",
    "MarketDataPoint",
    "PriceData",
    "ValuationMetrics",
    "TechnicalIndicators",
    "RiskAnalytics",
    "MarketDataInput",
    "BenchmarkData",
    "MarketDataSummary",
    # Relationship models
    "BaseEntity",
    "FundReference",
    "UserReference",
    "ReportReference",
    "FundSubscription",
    "UserPortfolio",
    "AuditLog",
    "FundHierarchy",
    "InvestmentStrategy",
    "FundManager",
    "ReportSchedule",
    "UserFundAccess",
    "FundBenchmark",
    "FundComparison",
    "Notification",
    "FundDataSource",
    "RelationshipHelpers",
    # Request models
    "FundCreateRequest",
    "FundUpdateRequest",
    "FundQueryRequest",
    "UserCreateRequest",
    "UserUpdateRequest",
    "UserQueryRequest",
    "UserPasswordChangeRequest",
    "UserPasswordResetRequest",
    "UserLoginRequest",
    "UserRegisterRequest",
    "TokenRefreshRequest",
    "ConfirmRegistrationRequest",
    "ReportCreateRequest",
    "ReportUpdateRequest",
    "ReportQueryRequest",
    "ReportGenerateRequest",
    "BulkOperationRequest",
    "PaginationRequest",
    "SearchRequest",
    # Response models
    "BaseResponse",
    "ErrorResponse",
    "PaginatedResponse",
    "SingleResponse",
    "ListResponse",
    "BulkOperationResponse",
    "FundSummaryResponse",
    "FundDetailResponse",
    "FundCreateResponse",
    "FundUpdateResponse",
    "FundListResponse",
    "FundSearchResponse",
    "UserSummaryResponse",
    "UserDetailResponse",
    "UserProfileResponse",
    "UserCreateResponse",
    "UserUpdateResponse",
    "UserListResponse",
    "UserSearchResponse",
    "UserPasswordChangeResponse",
    "UserLoginResponse",
    "UserRegisterResponse",
    "TokenRefreshResponse",
    "ConfirmRegistrationResponse",
    "ReportSummaryResponse",
    "ReportDetailResponse",
    "ReportCreateResponse",
    "ReportUpdateResponse",
    "ReportListResponse",
    "ReportGenerateResponse",
    "ReportDownloadResponse",
    "FundStatisticsResponse",
    "UserStatisticsResponse",
    "ReportStatisticsResponse",
    "HealthCheckResponse",
    "SystemInfoResponse",
    # API utilities
    "ModelConverter",
    "PublicFundResponse",
    "AdminUserResponse",
    "AuditLogEntry",
    "ValidationUtils",
    "QueryBuilder",
    "ResponseFormatter",
    # DynamoDB mapping
    "DynamoDBMapper",
    "DynamoDBTypeConverter",
    "FundDynamoDBMapper",
    "UserDynamoDBMapper",
    "ReportDynamoDBMapper",
    "BatchDynamoDBMapper",
    # Validation utilities
    "CommonValidators",
    "BusinessLogicValidators",
    # Portfolio models
    "Portfolio",
    "PortfolioHolding",
    "PortfolioTransaction",
    "PortfolioPerformance",
    "PortfolioStatus",
    "TransactionType",
    "PortfolioType",
    "PortfolioCreateRequest",
    "PortfolioUpdateRequest",
    "PortfolioResponse",
    "AddHoldingRequest",
    "AddTransactionRequest",
    "PortfolioDynamoDBItem",
]
