'use client';

import { useState, useEffect } from 'react';

import { Save, Cancel, TrendingUp, Assessment } from '@mui/icons-material';

import { Card, Button, Input } from '@/components/ui';
import { useTranslation } from '@/i18n/provider';
import { FundDetails } from '@/types';

interface KPIRiskMetricsFormProps {
  fund: FundDetails;
  onSubmit: (data: any) => void;
  onCancel: () => void;
  disabled?: boolean;
}

export default function KPIRiskMetricsForm({ fund, onSubmit, onCancel, disabled }: KPIRiskMetricsFormProps) {
  const { t } = useTranslation();
  const [loading, setLoading] = useState(false);
  const [formData, setFormData] = useState({
    // Key Performance Indicators
    kpis: {
      totalReturn: fund.analytics?.kpis?.totalReturn || '',
      annualizedReturn: fund.analytics?.kpis?.annualizedReturn || '',
      volatility: fund.analytics?.kpis?.volatility || '',
      sharpeRatio: fund.analytics?.kpis?.sharpeRatio || '',
      sortinoRatio: fund.analytics?.kpis?.sortinoRatio || '',
      calmarRatio: fund.analytics?.kpis?.calmarRatio || '',
      informationRatio: fund.analytics?.kpis?.informationRatio || '',
      treynorRatio: fund.analytics?.kpis?.treynorRatio || '',
      alpha: fund.analytics?.kpis?.alpha || '',
      beta: fund.analytics?.kpis?.beta || '',
      maxDrawdown: fund.analytics?.kpis?.maxDrawdown || '',
      trackingError: fund.analytics?.kpis?.trackingError || '',
    },
    // Risk Metrics
    riskMetrics: {
      standardDeviation: fund.analytics?.riskMetrics?.standardDeviation || '',
      varRisk: fund.analytics?.riskMetrics?.varRisk || '',
      sortRatio: fund.analytics?.riskMetrics?.sortRatio || '',
      calmarRatio: fund.analytics?.riskMetrics?.calmarRatio || '',
      correlation: fund.analytics?.riskMetrics?.correlation || '',
    }
  });

  const handleInputChange = (section: 'kpis' | 'riskMetrics', field: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      [section]: {
        ...prev[section],
        [field]: value
      }
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    
    try {
      // Convert empty strings to null and numbers to proper format
      const processedData = {
        analytics: {
          ...fund.analytics,
          kpis: Object.entries(formData.kpis).reduce((acc, [key, value]) => {
            acc[key] = value === '' ? null : parseFloat(value as string);
            return acc;
          }, {} as any),
          riskMetrics: Object.entries(formData.riskMetrics).reduce((acc, [key, value]) => {
            acc[key] = value === '' ? null : parseFloat(value as string);
            return acc;
          }, {} as any)
        }
      };
      
      await onSubmit(processedData);
    } catch (error) {
      console.error('Error submitting KPI and Risk Metrics:', error);
    } finally {
      setLoading(false);
    }
  };

  const kpiFields = [
    { key: 'totalReturn', label: 'Total Return (%)', type: 'number', step: '0.01' },
    { key: 'annualizedReturn', label: 'Annualized Return (%)', type: 'number', step: '0.01' },
    { key: 'volatility', label: 'Volatility (%)', type: 'number', step: '0.01' },
    { key: 'sharpeRatio', label: 'Sharpe Ratio', type: 'number', step: '0.01' },
    { key: 'sortinoRatio', label: 'Sortino Ratio', type: 'number', step: '0.01' },
    { key: 'calmarRatio', label: 'Calmar Ratio', type: 'number', step: '0.01' },
    { key: 'informationRatio', label: 'Information Ratio', type: 'number', step: '0.01' },
    { key: 'treynorRatio', label: 'Treynor Ratio', type: 'number', step: '0.01' },
    { key: 'alpha', label: 'Alpha', type: 'number', step: '0.01' },
    { key: 'beta', label: 'Beta', type: 'number', step: '0.01' },
    { key: 'maxDrawdown', label: 'Max Drawdown (%)', type: 'number', step: '0.01' },
    { key: 'trackingError', label: 'Tracking Error (%)', type: 'number', step: '0.01' },
  ];

  const riskMetricsFields = [
    { key: 'standardDeviation', label: 'Standard Deviation (%)', type: 'number', step: '0.01' },
    { key: 'varRisk', label: 'Value at Risk (%)', type: 'number', step: '0.01' },
    { key: 'sortRatio', label: 'SORT Ratio', type: 'number', step: '0.01' },
    { key: 'calmarRatio', label: 'Calmar Ratio', type: 'number', step: '0.01' },
    { key: 'correlation', label: 'Correlation', type: 'number', step: '0.01', min: '-1', max: '1' },
  ];

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      {/* Key Performance Indicators Section */}
      <Card>
        <Card.Header>
          <Card.Title className="flex items-center gap-2">
            <TrendingUp className="w-5 h-5 text-blue-600" />
            Key Performance Indicators
          </Card.Title>
          <p className="text-sm text-gray-600 dark:text-gray-400">
            Update the fund's key performance metrics and ratios
          </p>
        </Card.Header>
        <Card.Content>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {kpiFields.map((field) => (
              <div key={field.key}>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  {field.label}
                </label>
                <Input
                  type={field.type}
                  step={field.step}
                  min={(field as any).min}
                  max={(field as any).max}
                  value={formData.kpis[field.key as keyof typeof formData.kpis]}
                  onChange={(e) => handleInputChange('kpis', field.key, e.target.value)}
                  placeholder={`Enter ${field.label.toLowerCase()}`}
                  disabled={disabled || loading}
                />
              </div>
            ))}
          </div>
        </Card.Content>
      </Card>

      {/* Risk Metrics Section */}
      <Card>
        <Card.Header>
          <Card.Title className="flex items-center gap-2">
            <Assessment className="w-5 h-5 text-red-600" />
            Risk Metrics
          </Card.Title>
          <p className="text-sm text-gray-600 dark:text-gray-400">
            Update the fund's risk assessment and measurement metrics
          </p>
        </Card.Header>
        <Card.Content>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {riskMetricsFields.map((field) => (
              <div key={field.key}>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  {field.label}
                </label>
                <Input
                  type={field.type}
                  step={field.step}
                  min={(field as any).min}
                  max={(field as any).max}
                  value={formData.riskMetrics[field.key as keyof typeof formData.riskMetrics]}
                  onChange={(e) => handleInputChange('riskMetrics', field.key, e.target.value)}
                  placeholder={`Enter ${field.label.toLowerCase()}`}
                  disabled={disabled || loading}
                />
              </div>
            ))}
          </div>
        </Card.Content>
      </Card>

      {/* Action Buttons */}
      <div className="flex justify-end gap-3 pt-6 border-t border-gray-200 dark:border-gray-700">
        <Button
          type="button"
          variant="outline"
          onClick={onCancel}
          disabled={disabled || loading}
          className="flex items-center gap-2"
        >
          <Cancel className="w-4 h-4" />
          Cancel
        </Button>
        <Button
          type="submit"
          disabled={disabled || loading}
          className="flex items-center gap-2"
        >
          {loading ? (
            <>
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
              Saving...
            </>
          ) : (
            <>
              <Save className="w-4 h-4" />
              Save KPI & Risk Metrics
            </>
          )}
        </Button>
      </div>
    </form>
  );
}